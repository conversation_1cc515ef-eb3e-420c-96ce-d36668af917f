/*
 * =================================================================
 * FILE: src/app/api/outcomes/route.js
 * =================================================================
 * This new API route handles creating a new outcome for the user.
 */

import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt from "jsonwebtoken";
import prisma from "@/lib/prisma";

const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-key-that-is-long";
const COOKIE_NAME = "authToken";

export async function POST(request) {
  try {
    // 1. Authenticate the user
    const cookieStore = await cookies();
    const token = cookieStore.get(COOKIE_NAME);
    if (!token) return NextResponse.json({ message: "Authentication required" }, { status: 401 });

    const { userId } = jwt.verify(token.value, JWT_SECRET);
    if (!userId) return NextResponse.json({ message: "Invalid token payload" }, { status: 401 });

    // 2. Get the new outcome data from the request
    const body = await request.json();
    const { name, projectId, priorityId, priorityFieldId } = body;

    // 3. Validate the input
    if (!name || !projectId || !priorityFieldId) {
      return NextResponse.json({ message: "Missing required fields (name, projectId, priorityFieldId)" }, { status: 400 });
    }

    // 4. Create the new outcome and its custom field values in a transaction
    const newOutcome = await prisma.workitems_outcome.create({
      data: {
        user_id: userId,
        name: name,
        project_id: projectId,
        // If a priority was selected, create the custom field value for it
        ...(priorityId &&
          priorityId !== "undefined" && {
            custom_field_values: {
              create: [
                {
                  field_definition_id: priorityFieldId,
                  selected_options: {
                    connect: { id: priorityId },
                  },
                },
              ],
            },
          }),
      },
    });

    return NextResponse.json(newOutcome, { status: 201 }); // 201 Created
  } catch (error) {
    console.error("Error creating outcome:", error);
    return NextResponse.json({ message: "An internal server error occurred" }, { status: 500 });
  }
}
