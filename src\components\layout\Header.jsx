// We'll use the 'Link' component from Next.js for client-side navigation
import Link from "next/link";
// We'll use a real icon library like lucide-react, which is great with Tailwind
import { CheckCircle } from "lucide-react";

export default function Header() {
  return (
    <header className="flex justify-between items-center">
      <Link href="/" className="flex items-center group">
        <CheckCircle className="w-7 h-7 text-blue-600 group-hover:text-blue-700 transition-colors" />
        <h1 className="text-2xl font-bold text-gray-800 ml-2 group-hover:text-gray-900 transition-colors">Clarity</h1>
      </Link>
      <nav className="flex items-center space-x-4">
        <Link href="/login" className="text-gray-600 hover:text-blue-600 transition duration-300">
          Login
        </Link>
        <Link href="/signup" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300 shadow-sm">
          Sign Up
        </Link>
      </nav>
    </header>
  );
}
