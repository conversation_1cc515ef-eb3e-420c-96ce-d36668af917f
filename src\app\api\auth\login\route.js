import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { serialize } from "cookie";

// A secret key to sign the JWT. Store this in your .env file in a real project!
const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-key-that-is-long";
const COOKIE_NAME = "authToken";

export async function POST(request) {
  try {
    const body = await request.json();
    const { identifier, password } = body;

    if (!identifier || !password) {
      return NextResponse.json({ message: "Missing identifier or password" }, { status: 400 });
    }

    const user = await prisma.auth_user.findFirst({
      where: {
        OR: [{ email: identifier }, { username: identifier }],
      },
    });

    if (!user || !(await bcrypt.compare(password, user.password))) {
      return NextResponse.json({ message: "Invalid credentials" }, { status: 401 });
    }

    // --- NEW SESSION LOGIC ---
    // 1. Create the JWT payload
    const tokenPayload = { userId: user.id, email: user.email };

    // 2. Sign the token
    const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: "7d" });

    // 3. Serialize the cookie
    const serializedCookie = serialize(COOKIE_NAME, token, {
      httpOnly: true, // The cookie can't be accessed by client-side JavaScript
      secure: process.env.NODE_ENV === "production", // Only send over HTTPS in production
      sameSite: "strict", // Helps prevent CSRF attacks
      maxAge: 60 * 60 * 24 * 7, // 1 week
      path: "/",
    });

    // 4. Update last_login
    await prisma.auth_user.update({
      where: { id: user.id },
      data: { last_login: new Date() },
    });

    const { password: _, ...userWithoutPassword } = user;

    // 5. Return the user data and set the cookie in the response headers
    const response = NextResponse.json(userWithoutPassword, { status: 200 });
    response.headers.set("Set-Cookie", serializedCookie);

    return response;
  } catch (error) {
    console.error("Login error:", error);
    return NextResponse.json({ message: "An internal server error occurred" }, { status: 500 });
  }
}
