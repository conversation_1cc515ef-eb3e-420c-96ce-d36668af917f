import FeatureCard from "@/components/ui/FeatureCard"; // Assuming '@/' is configured to point to 'src/'
import { Compass, Target, LineChart } from "lucide-react"; // Using better icons

export default function FeaturesSection() {
  return (
    <section className="mt-8 sm:mt-8">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-5xl mx-auto text-center">
        <FeatureCard
          icon={
            <div className="bg-blue-100 text-blue-600 rounded-full p-3 inline-block">
              <Compass />
            </div>
          }
          title="Define Your Life Areas"
        >
          Organize your efforts across key aspects of your life, from career and health to family and personal growth.
        </FeatureCard>
        <FeatureCard
          icon={
            <div className="bg-green-100 text-green-600 rounded-full p-3 inline-block">
              <Target />
            </div>
          }
          title="Focus on Outcomes"
        >
          Shift from endless to-do lists to planning tangible, weekly results that move you closer to your goals.
        </FeatureCard>
        <FeatureCard
          icon={
            <div className="bg-indigo-100 text-indigo-600 rounded-full p-3 inline-block">
              <LineChart />
            </div>
          }
          title="Review and Adapt"
        >
          Reflect on your weekly achievements and struggles to build momentum and consistently improve your system.
        </FeatureCard>
      </div>
    </section>
  );
}
