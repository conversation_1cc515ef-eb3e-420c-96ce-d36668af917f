"use client";

import { useState } from "react";

export default function Outcomes({ params }) {
  const [outcomesData, setOutcomesData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchOutcomes = async () => {
    try {
      const response = await fetch("/api/outcomes");
      if (!response.ok) {
        if (response.status === 401) {
          // User is not authenticated, set an error message
          setError("Authentication failed. Please log in again.");
        } else {
          throw new Error("Failed to fetch outcomes data");
        }
        return;
      }
      const data = await response.json();
      setOutcomesData(data);
      console.log("Outcomes: ", data);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <>
      <h1>Outcomes : </h1>
      <button onClick={fetchOutcomes} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-300 shadow-sm">
        Fetch Outcomes
      </button>
      {outcomesData && (
        <div>
          {outcomesData.map((outcome) => (
            <div key={outcome.id}>{outcome.name}</div>
          ))}
        </div>
      )}
    </>
  );
}
