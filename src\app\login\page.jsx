"use client";

import Link from "next/link";
import { LogIn, CheckCircle } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";

import Header from "@/components/layout/Header";

export default function LoginPage() {
  const [formData, setFormData] = useState({
    identifier: "",
    password: "",
  });
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        // If response is not OK, use the message from the API
        throw new Error(data.message || "Something went wrong");
      }

      // On success, get the user's first name for the greeting
      const userName = data.first_name || data.username;

      // Redirect to the dashboard, passing the name as a query parameter
      router.push(`/dashboard?name=${encodeURIComponent(userName)}`);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="font-sans bg-gray-50 text-gray-800">
        <div className="container mx-auto px-6 py-4">
          <Header />
        </div>
      </div>
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center items-center">
        <div className="w-full max-w-md p-8 bg-white rounded-xl shadow-lg">
          <Link href="/" className="flex justify-center items-center mb-6 group">
            <CheckCircle className="w-8 h-8 text-blue-600 group-hover:text-blue-700 transition-colors" />
            <h1 className="text-3xl font-bold text-gray-800 ml-2 group-hover:text-gray-900 transition-colors">Clarity</h1>
          </Link>
          <h2 className="text-2xl font-semibold text-center text-gray-700 mb-6">Welcome Back</h2>

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="identifier" className="block text-sm font-medium text-gray-600 mb-1">
                Email or Username
              </label>
              <input
                type="text"
                id="identifier"
                name="identifier"
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition"
                value={formData.identifier}
                onChange={handleChange}
              />
            </div>

            <div className="mb-6">
              <label htmlFor="password" className="block text-sm font-medium text-gray-600 mb-1">
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                required
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition"
                value={formData.password}
                onChange={handleChange}
              />
              <a href="#" className="text-xs text-blue-600 hover:underline mt-1 block text-right">
                Forgot Password?
              </a>
            </div>

            {error && <p className="text-red-500 text-sm text-center mb-4">{error}</p>}

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2.5 rounded-lg font-semibold hover:bg-blue-700 transition duration-300 flex items-center justify-center disabled:bg-blue-400"
            >
              <LogIn className="w-5 h-5 mr-2" />
              {isLoading ? "Logging In..." : "Log In"}
            </button>
          </form>
        </div>
        <p className="mt-6 text-center text-sm text-gray-600">
          Don't have an account?{" "}
          <Link href="/signup" className="font-semibold text-blue-600 hover:underline">
            Sign Up
          </Link>
        </p>
      </div>
    </>
  );
}
