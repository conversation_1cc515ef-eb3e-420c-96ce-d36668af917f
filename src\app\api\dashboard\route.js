/*
 * =================================================================
 * FILE: src/app/api/dashboard/route.js (UPDATED)
 * =================================================================
 * This API route is now enhanced to fetch all nested data required
 * by the dashboard, including project and custom field details.
 */

import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt from "jsonwebtoken";
import prisma from "@/lib/prisma";

const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-key-that-is-long";
const COOKIE_NAME = "authToken";

export async function GET(request) {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get(COOKIE_NAME);

    if (!token) {
      return NextResponse.json({ message: "Authentication required" }, { status: 401 });
    }

    const { userId } = jwt.verify(token.value, JWT_SECRET);
    if (!userId) {
      return NextResponse.json({ message: "Invalid token payload" }, { status: 401 });
    }

    const [lifeAspects, customFieldDefinitions, outcomes] = await Promise.all([
      prisma.workitems_life_aspect.findMany({
        where: { user_id: userId },
        orderBy: { sort_order: "asc" },
      }),
      prisma.workitems_custom_field_definition.findMany({
        where: { user_id: userId },
        include: {
          choice_options: { orderBy: { sort_order: "asc" } },
        },
        orderBy: { sort_order: "asc" },
      }),
      // NEW: Updated query to include all necessary relations for outcomes
      prisma.workitems_outcome.findMany({
        where: { user_id: userId },
        include: {
          project: { select: { life_aspect_id: true } },
          custom_field_values: {
            include: {
              selected_options: { select: { id: true } },
            },
          },
        },
        orderBy: { sort_order: "asc" },
      }),
    ]);
    console.log("Outcomes: ", outcomes);
    console.log("Life Aspects: ", lifeAspects);
    console.log("Custom Field Definitions: ", customFieldDefinitions);

    const dashboardData = {
      lifeAspects,
      customFieldDefinitions,
      outcomes,
    };

    return NextResponse.json(dashboardData, { status: 200 });
  } catch (error) {
    console.error("Dashboard API error:", error);
    return NextResponse.json({ message: "An internal server error occurred" }, { status: 500 });
  }
}
