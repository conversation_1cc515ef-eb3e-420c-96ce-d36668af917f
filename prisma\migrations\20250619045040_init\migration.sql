-- CreateTable
CREATE TABLE "auth_user" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "is_staff" BOOLEAN NOT NULL DEFAULT false,
    "is_superuser" BOOLEAN NOT NULL DEFAULT false,
    "date_joined" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_login" TIMESTAMP(3),

    CONSTRAINT "auth_user_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "auth_user_preferences" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "preferences" JSONB NOT NULL DEFAULT '{}',
    "week_starts_on" VARCHAR(10) NOT NULL DEFAULT 'Sunday',
    "enable_inheritance" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "auth_user_preferences_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workitems_custom_field_definition" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "field_type" TEXT NOT NULL,
    "is_required" BOOLEAN NOT NULL DEFAULT false,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workitems_custom_field_definition_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workitems_custom_field_choice_option" (
    "id" TEXT NOT NULL,
    "field_definition_id" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "color" TEXT NOT NULL DEFAULT '#3498db',
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "is_default" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workitems_custom_field_choice_option_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workitems_life_aspect" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "color" TEXT NOT NULL DEFAULT '#3498db',
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workitems_life_aspect_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workitems_project" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "life_aspect_id" TEXT,
    "parent_project_id" TEXT,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workitems_project_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workitems_outcome" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "project_id" TEXT NOT NULL,
    "sort_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "workitems_outcome_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "workitems_custom_field_value" (
    "id" TEXT NOT NULL,
    "field_definition_id" TEXT NOT NULL,
    "project_id" TEXT,
    "outcome_id" TEXT,
    "text_value" TEXT,

    CONSTRAINT "workitems_custom_field_value_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_workitems_custom_field_choice_optionToworkitems_custom_field_v" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_workitems_custom_field_choice_optionToworkitems_custom_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "auth_user_email_key" ON "auth_user"("email");

-- CreateIndex
CREATE UNIQUE INDEX "auth_user_username_key" ON "auth_user"("username");

-- CreateIndex
CREATE UNIQUE INDEX "auth_user_preferences_user_id_key" ON "auth_user_preferences"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "workitems_custom_field_definition_user_id_name_key" ON "workitems_custom_field_definition"("user_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "workitems_custom_field_choice_option_field_definition_id_va_key" ON "workitems_custom_field_choice_option"("field_definition_id", "value");

-- CreateIndex
CREATE UNIQUE INDEX "workitems_life_aspect_user_id_name_key" ON "workitems_life_aspect"("user_id", "name");

-- CreateIndex
CREATE UNIQUE INDEX "workitems_custom_field_value_field_definition_id_project_id_key" ON "workitems_custom_field_value"("field_definition_id", "project_id");

-- CreateIndex
CREATE UNIQUE INDEX "workitems_custom_field_value_field_definition_id_outcome_id_key" ON "workitems_custom_field_value"("field_definition_id", "outcome_id");

-- CreateIndex
CREATE INDEX "_workitems_custom_field_choice_optionToworkitems_custom_B_index" ON "_workitems_custom_field_choice_optionToworkitems_custom_field_v"("B");

-- AddForeignKey
ALTER TABLE "auth_user_preferences" ADD CONSTRAINT "auth_user_preferences_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_custom_field_definition" ADD CONSTRAINT "workitems_custom_field_definition_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_custom_field_choice_option" ADD CONSTRAINT "workitems_custom_field_choice_option_field_definition_id_fkey" FOREIGN KEY ("field_definition_id") REFERENCES "workitems_custom_field_definition"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_life_aspect" ADD CONSTRAINT "workitems_life_aspect_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_project" ADD CONSTRAINT "workitems_project_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_project" ADD CONSTRAINT "workitems_project_life_aspect_id_fkey" FOREIGN KEY ("life_aspect_id") REFERENCES "workitems_life_aspect"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_project" ADD CONSTRAINT "workitems_project_parent_project_id_fkey" FOREIGN KEY ("parent_project_id") REFERENCES "workitems_project"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_outcome" ADD CONSTRAINT "workitems_outcome_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth_user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_outcome" ADD CONSTRAINT "workitems_outcome_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "workitems_project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_custom_field_value" ADD CONSTRAINT "workitems_custom_field_value_field_definition_id_fkey" FOREIGN KEY ("field_definition_id") REFERENCES "workitems_custom_field_definition"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_custom_field_value" ADD CONSTRAINT "workitems_custom_field_value_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "workitems_project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workitems_custom_field_value" ADD CONSTRAINT "workitems_custom_field_value_outcome_id_fkey" FOREIGN KEY ("outcome_id") REFERENCES "workitems_outcome"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_workitems_custom_field_choice_optionToworkitems_custom_field_v" ADD CONSTRAINT "_workitems_custom_field_choice_optionToworkitems_custom__A_fkey" FOREIGN KEY ("A") REFERENCES "workitems_custom_field_choice_option"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_workitems_custom_field_choice_optionToworkitems_custom_field_v" ADD CONSTRAINT "_workitems_custom_field_choice_optionToworkitems_custom__B_fkey" FOREIGN KEY ("B") REFERENCES "workitems_custom_field_value"("id") ON DELETE CASCADE ON UPDATE CASCADE;
