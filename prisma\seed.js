/*
 * =================================================================
 * FILE: prisma/seed.js (UPDATED)
 * =================================================================
 * This script now also seeds a default list of "Top Outcomes".
 */

const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcryptjs");
const prisma = new PrismaClient();

async function main() {
  console.log(`Start seeding ...`);

  // --- 1. Delete all existing data ---
  console.log("Deleting existing data...");
  await prisma.workitems_custom_field_value.deleteMany();
  await prisma.workitems_outcome.deleteMany();
  await prisma.workitems_project.deleteMany();
  await prisma.workitems_custom_field_choice_option.deleteMany();
  await prisma.workitems_custom_field_definition.deleteMany();
  await prisma.workitems_life_aspect.deleteMany();
  await prisma.auth_user_preferences.deleteMany();
  await prisma.auth_user.deleteMany();
  console.log("Existing data deleted.");

  // --- 2. Create a single test user ---
  const hashedPassword = await bcrypt.hash("password123", 10);
  const user = await prisma.auth_user.create({
    data: {
      email: "<EMAIL>",
      username: "RatnaShiva",
      password: hashedPassword,
      first_name: "Ratna",
      last_name: "Teja",
    },
  });
  console.log(`Created user with id: ${user.id}`);

  // --- 3. Seed data for the test user ---
  console.log("Seeding data for the test user...");

  // Create Life Aspects, Projects, and Priority Field...
  const mindAspect = await prisma.workitems_life_aspect.create({ data: { user_id: user.id, name: "Mind" } });
  const bodyAspect = await prisma.workitems_life_aspect.create({ data: { user_id: user.id, name: "Body" } });
  const priorityField = await prisma.workitems_custom_field_definition.create({
    data: {
      user_id: user.id,
      name: "Priority",
      field_type: "SINGLE_SELECT",
      choice_options: { create: [{ value: "Urgent" }, { value: "Not Urgent" }] },
    },
    include: { choice_options: true },
  });
  const urgentChoice = priorityField.choice_options[0];
  const mindProject = await prisma.workitems_project.create({ data: { user_id: user.id, name: "Mindfulness", life_aspect_id: mindAspect.id } });
  const bodyProject = await prisma.workitems_project.create({ data: { user_id: user.id, name: "Fitness", life_aspect_id: bodyAspect.id } });

  // Create Outcomes...
  const outcome1 = await prisma.workitems_outcome.create({
    data: {
      user_id: user.id,
      project_id: bodyProject.id,
      name: "Go for a walk",
      custom_field_values: { create: [{ field_definition_id: priorityField.id, selected_options: { connect: { id: urgentChoice.id } } }] },
    },
  });
  const outcome2 = await prisma.workitems_outcome.create({ data: { user_id: user.id, project_id: mindProject.id, name: "Read a book" } });

  // --- 4. NEW: Seed the Top Outcomes preference ---
  console.log("Seeding Top Outcomes preference...");
  await prisma.auth_user_preferences.create({
    data: {
      user_id: user.id,
      preferences: {
        topOutcomes: [outcome1.id, outcome2.id], // Pin the two outcomes we just created
      },
    },
  });

  console.log(`Seeding finished.`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
