/*
 * =================================================================
 * FILE: src/app/dashboard/page.js (UPDATED with Create Outcome UI)
 * =================================================================
 * This dashboard now includes the "New Outcome" button and modal form.
 */

"use client";

import { useState, useEffect, Fragment, useCallback } from "react";
import { ChevronLeft, ChevronRight, Settings, X, PlusCircle } from "lucide-react";
import { format, addDays, subDays, startOfWeek, endOfWeek, getISOWeek } from "date-fns";
import { DndContext, useDraggable, useDroppable, closestCenter, DragOverlay } from "@dnd-kit/core";
import { SortableContext, useSortable, arrayMove, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Dialog, Transition } from "@headlessui/react";

// --- Reusable Components ---
const PaperSection = ({ title, children, onCustomizeClick, onNewClick }) => (
  <div className="mb-12">
    <div className="flex justify-between items-center mb-4">
      <h2 className="text-2xl font-serif font-bold text-gray-700">{title}</h2>
      <div className="flex items-center space-x-2">
        {onNewClick && (
          <button onClick={onNewClick} className="p-2 rounded-full hover:bg-gray-200 transition-colors">
            <PlusCircle className="w-5 h-5 text-gray-600" />
          </button>
        )}
        {onCustomizeClick && (
          <button onClick={onCustomizeClick} className="p-2 rounded-full hover:bg-gray-200 transition-colors">
            <Settings className="w-5 h-5 text-gray-600" />
          </button>
        )}
      </div>
    </div>
    {children}
  </div>
);
const OutcomeCardUI = ({ outcome, isDragging }) => (
  <div className={`bg-white rounded-md p-3 text-sm border border-gray-200 text-gray-900 ${isDragging ? "opacity-50 shadow-2xl" : "shadow-sm"}`}>
    <p>{outcome.name}</p>
  </div>
);
const DraggableOutcomeCard = ({ outcome }) => {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({ id: `matrix-${outcome.id}`, data: { outcome } });
  return (
    <div ref={setNodeRef} style={{ transform: CSS.Translate.toString(transform) }} {...listeners} {...attributes} className="cursor-grab">
      <OutcomeCardUI outcome={outcome} />
    </div>
  );
};
const SortableOutcomeCard = ({ outcome }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: outcome.id });
  return (
    <div ref={setNodeRef} style={{ transform: CSS.Transform.toString(transform), transition }} {...listeners} {...attributes} className="cursor-grab">
      <OutcomeCardUI outcome={outcome} isDragging={isDragging} />
    </div>
  );
};
const DroppableTableCell = ({ rowId, subRowId, colId, children }) => {
  const { isOver, setNodeRef } = useDroppable({ id: `${rowId}-${subRowId}-${colId}`, data: { type: "cell", rowId, subRowId, colId } });
  return (
    <td ref={setNodeRef} className={`p-2 align-top border-l border-gray-200 min-h-[60px] transition-colors ${isOver ? "bg-blue-100" : ""}`}>
      <div className="space-y-2">{children}</div>
    </td>
  );
};
const WeekNavigator = () => {
  /* Unchanged */
};
const TopOutcomesList = ({ topOutcomeIds = [], allOutcomes = [] }) => {
  /* Unchanged */
};
const CustomizeViewModal = ({ isOpen, onClose, currentView, onSave, customFields = [] }) => {
  /* Unchanged */
};

// --- NEW: Create Outcome Modal ---
const CreateOutcomeModal = ({ isOpen, onClose, projects, priorityField, onSave }) => {
  const [name, setName] = useState("");
  const [projectId, setProjectId] = useState("");
  const [priorityId, setPriorityId] = useState("");

  useEffect(() => {
    if (projects.length > 0) setProjectId(projects[0].id);
    if (priorityField?.choice_options?.length > 0) setPriorityId(priorityField.choice_options[0].id);
  }, [projects, priorityField]);

  const handleSave = () => {
    if (!name.trim()) return; // Simple validation
    onSave({
      name,
      projectId,
      priorityId,
      priorityFieldId: priorityField.id,
    });
    setName(""); // Reset form
    onClose();
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 flex justify-between items-center">
                  New Outcome
                  <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-200">
                    <X className="w-4 h-4" />
                  </button>
                </Dialog.Title>
                <div className="mt-4 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Outcome Name</label>
                    <input
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-gray-900"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Project</label>
                    <select
                      value={projectId}
                      onChange={(e) => setProjectId(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-gray-900"
                    >
                      {projects.map((p) => (
                        <option key={p.id} value={p.id}>
                          {p.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  {priorityField && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">{priorityField.name}</label>
                      <select
                        value={priorityId}
                        onChange={(e) => setPriorityId(e.target.value)}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-gray-900"
                      >
                        {priorityField.choice_options.map((c) => (
                          <option key={c.id} value={c.id}>
                            {c.value}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
                <div className="mt-6 flex justify-end">
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none"
                    onClick={handleSave}
                  >
                    Create Outcome
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

const DynamicMatrix = ({ data, setData, onCustomizeClick, onNewClick }) => {
  /* ... existing component logic ... */
};

export default function DashboardPage() {
  const [matrixData, setMatrixData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isCustomizeModalOpen, setIsCustomizeModalOpen] = useState(false);
  const [isNewOutcomeModalOpen, setIsNewOutcomeModalOpen] = useState(false); // New state for modal

  const [view, setView] = useState({
    rows: "lifeAspect",
    subRows: "project",
    columns: "priority",
  });

  // fetchData remains the same
  const fetchData = useCallback(async (currentView) => {
    /* ... */
  }, []);
  useEffect(() => {
    fetchData(view);
  }, [fetchData, view]);

  // handleSaveView remains the same
  const handleSaveView = async (newView) => {
    /* ... */
  };

  // NEW: Handler for creating a new outcome
  const handleCreateOutcome = async (outcomeData) => {
    console.log("Creating new outcome:", outcomeData);
    // We will add the API call and data refetching logic here in the next step
  };

  if (isLoading) return <div className="flex justify-center items-center min-h-screen">Loading dashboard...</div>;
  if (error) return <div className="flex justify-center items-center min-h-screen text-red-500">Error: {error}</div>;

  const priorityField = matrixData?.customFields?.find((cf) => cf.name.toLowerCase() === "priority");

  return (
    <div className="bg-[#F7F7F7] min-h-screen p-4 sm:p-8 md:p-12 font-sans">
      <div className="max-w-screen-xl mx-auto bg-white p-8 sm:p-12 rounded-lg shadow-md">
        <WeekNavigator />
        {/* Updated to pass onNewClick handler */}
        {matrixData && (
          <DynamicMatrix
            data={matrixData}
            setData={setMatrixData}
            onCustomizeClick={() => setIsCustomizeModalOpen(true)}
            onNewClick={() => setIsNewOutcomeModalOpen(true)}
          />
        )}
      </div>

      <CustomizeViewModal
        isOpen={isCustomizeModalOpen}
        onClose={() => setIsCustomizeModalOpen(false)}
        currentView={view}
        onSave={handleSaveView}
        customFields={matrixData?.customFields || []}
      />

      {/* NEW: Render the Create Outcome Modal */}
      <CreateOutcomeModal
        isOpen={isNewOutcomeModalOpen}
        onClose={() => setIsNewOutcomeModalOpen(false)}
        projects={matrixData?.projects.filter((p) => p.id !== "undefined") || []}
        priorityField={priorityField}
        onSave={handleCreateOutcome}
      />
    </div>
  );
}

// NOTE: All other components (DynamicMatrix, etc.) are required for this file to work.
// I have included stubs where their code is unchanged for brevity. You should use the full
// versions from the previous step.
