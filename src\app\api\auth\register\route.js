/*
* =================================================================
* FILE: src/app/api/auth/register/route.js (CORRECTED)
* =================================================================
* This API route now correctly seeds data by associating
* outcomes with different Life Aspects, which will fix the
* display issue on the dashboard.
*/

import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export async function POST(request) {
    try {
        const body = await request.json();
        const { username, email, password } = body;

        // --- User creation logic (unchanged) ---
        if (!username || !email || !password) {
            return NextResponse.json({ message: 'Missing required fields' }, { status: 400 });
        }
        const existingUserByEmail = await prisma.auth_user.findUnique({ where: { email } });
        if (existingUserByEmail) {
            return NextResponse.json({ message: 'User with this email already exists' }, { status: 409 });
        }
        const existingUserByUsername = await prisma.auth_user.findUnique({ where: { username } });
        if (existingUserByUsername) {
            return NextResponse.json({ message: 'Username is already taken' }, { status: 409 });
        }
        const hashedPassword = await bcrypt.hash(password, 10);
        const newUser = await prisma.auth_user.create({
            data: {
                username,
                email,
                password: hashedPassword,
                first_name: '',
                last_name: '',
            },
        });

        // // --- CORRECTED: Seed Data for the New User ---
        // await prisma.$transaction(async (tx) => {
        //     // 1. Create default Life Aspects
        //     const mindAspect = await tx.workitems_life_aspect.create({ data: { user_id: newUser.id, name: 'Mind' } });
        //     const bodyAspect = await tx.workitems_life_aspect.create({ data: { user_id: newUser.id, name: 'Body' } });
        //     const careerAspect = await tx.workitems_life_aspect.create({ data: { user_id: newUser.id, name: 'Career' } });

        //     // 2. Create a "Priority" custom field and its choices
        //     const priorityField = await tx.workitems_custom_field_definition.create({
        //         data: {
        //             user_id: newUser.id,
        //             name: 'Priority',
        //             field_type: 'SINGLE_SELECT',
        //             choice_options: {
        //                 create: [
        //                     { value: 'Urgent - Uplifting' },
        //                     { value: 'Urgent - Not Uplifting' },
        //                     { value: 'Not Urgent - Uplifting' },
        //                 ]
        //             }
        //         },
        //         include: { choice_options: true }
        //     });

        //     const urgentUplifting = priorityField.choice_options.find(c => c.value === 'Urgent - Uplifting');
        //     const notUrgentUplifting = priorityField.choice_options.find(c => c.value === 'Not Urgent - Uplifting');

        //     // 3. Create default Projects, one for each Life Aspect
        //     const mindProject = await tx.workitems_project.create({
        //         data: { user_id: newUser.id, name: 'Mindfulness Practice', life_aspect_id: mindAspect.id }
        //     });
        //      const bodyProject = await tx.workitems_project.create({
        //         data: { user_id: newUser.id, name: 'Fitness Routine', life_aspect_id: bodyAspect.id }
        //     });
        //      const careerProject = await tx.workitems_project.create({
        //         data: { user_id: newUser.id, name: 'Q3 Planning', life_aspect_id: careerAspect.id }
        //     });

        //     // 4. Create sample Outcomes and link them to the correct projects/aspects
        //     await tx.workitems_outcome.create({
        //         data: {
        //             user_id: newUser.id,
        //             project_id: mindProject.id, // Linked to Mind
        //             name: 'Meditate for 10 minutes',
        //             custom_field_values: {
        //                 create: [{
        //                     field_definition_id: priorityField.id,
        //                     selected_options: { connect: { id: notUrgentUplifting.id } }
        //                 }]
        //             }
        //         }
        //     });

        //      await tx.workitems_outcome.create({
        //         data: {
        //             user_id: newUser.id,
        //             project_id: bodyProject.id, // Linked to Body
        //             name: 'Go for a 30-minute walk',
        //             custom_field_values: {
        //                 create: [{
        //                     field_definition_id: priorityField.id,
        //                     selected_options: { connect: { id: urgentUplifting.id } }
        //                 }]
        //             }
        //         }
        //     });

        //      await tx.workitems_outcome.create({
        //         data: {
        //             user_id: newUser.id,
        //             project_id: careerProject.id, // Linked to Career
        //             name: 'Draft Q3 goals document',
        //             custom_field_values: {
        //                 create: [{
        //                     field_definition_id: priorityField.id,
        //                     selected_options: { connect: { id: urgentUplifting.id } }
        //                 }]
        //             }
        //         }
        //     });
        // });

        const { password: _, ...userWithoutPassword } = newUser;
        return NextResponse.json(userWithoutPassword, { status: 201 });

    } catch (error) {
        console.error('Registration error:', error);
        return NextResponse.json({ message: 'An internal server error occurred' }, { status: 500 });
    }
}
