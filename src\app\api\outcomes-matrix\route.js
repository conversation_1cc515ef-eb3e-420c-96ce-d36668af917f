/*
 * =================================================================
 * FILE: src/app/api/outcomes-matrix/route.js
 * =================================================================
 * This dedicated API route fetches and transforms data specifically
 * for the "This Week's Outcomes" matrix on the dashboard.
 */

import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt from "jsonwebtoken";
import prisma from "@/lib/prisma";

const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-key-that-is-long";
const COOKIE_NAME = "authToken";

export async function GET(request) {
  try {
    const cookieStore = cookies();
    const token = cookieStore.get(COOKIE_NAME);

    if (!token) {
      return NextResponse.json({ message: "Authentication required" }, { status: 401 });
    }

    const { userId } = jwt.verify(token.value, JWT_SECRET);
    if (!userId) {
      return NextResponse.json({ message: "Invalid token payload" }, { status: 401 });
    }

    const [lifeAspects, customFieldDefinitions, outcomes] = await Promise.all([
      prisma.workitems_life_aspect.findMany({
        where: { user_id: userId },
        orderBy: { sort_order: "asc" },
      }),
      prisma.workitems_custom_field_definition.findMany({
        where: { user_id: userId },
        include: {
          choice_options: { orderBy: { sort_order: "asc" } },
        },
      }),
      prisma.workitems_outcome.findMany({
        where: { user_id: userId },
        include: {
          project: { select: { life_aspect_id: true } },
          custom_field_values: {
            include: {
              selected_options: { select: { id: true, value: true } },
            },
          },
        },
      }),
    ]);

    // --- Data Transformation Logic ---
    const priorityField = customFieldDefinitions.find((f) => f.name.toLowerCase() === "priority");

    if (!priorityField) {
      return NextResponse.json({
        rows: [],
        columns: lifeAspects,
        grid: {},
      });
    }

    const priorities = priorityField.choice_options;

    const grid = {};
    priorities.forEach((p) => {
      grid[p.id] = {};
      lifeAspects.forEach((la) => {
        grid[p.id][la.id] = [];
      });
    });

    outcomes.forEach((outcome) => {
      const priorityValue = outcome.custom_field_values?.find((v) => v.field_definition_id === priorityField.id);
      const priorityChoiceId = priorityValue?.selected_options[0]?.id;
      const lifeAspectId = outcome.project?.life_aspect_id;

      if (priorityChoiceId && lifeAspectId && grid[priorityChoiceId] && grid[priorityChoiceId][lifeAspectId]) {
        grid[priorityChoiceId][lifeAspectId].push({
          id: outcome.id,
          name: outcome.name,
        });
      }
    });

    const matrixData = {
      rows: priorities.map((p) => ({ id: p.id, name: p.value })),
      columns: lifeAspects.map((la) => ({ id: la.id, name: la.name })),
      grid: grid,
    };

    console.log("Matrix Data : ", matrixData);
    return NextResponse.json(matrixData, { status: 200 });
  } catch (error) {
    console.error("Outcomes Matrix API error:", error);
    return NextResponse.json({ message: "An internal server error occurred" }, { status: 500 });
  }
}
