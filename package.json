{"name": "ninimisia_manual", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@headlessui/react": "^2.2.4", "@prisma/client": "^6.10.1", "bcryptjs": "^3.0.2", "cookie": "^1.0.2", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.517.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.10.1", "tailwindcss": "^4"}, "prisma": {"seed": "node prisma/seed.js"}}