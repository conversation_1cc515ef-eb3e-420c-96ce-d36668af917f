/*
 * =================================================================
 * FILE: src/app/api/user/preferences/route.js (UPDATED)
 * =================================================================
 * This API route is now updated to handle saving both the 'topOutcomes'
 * list and the new 'view' configuration object.
 */

import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import jwt from "jsonwebtoken";
import prisma from "@/lib/prisma";

const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-key-that-is-long";
const COOKIE_NAME = "authToken";

// --- Function to UPDATE user preferences ---
export async function PATCH(request) {
  try {
    // 1. Authenticate the user
    const cookieStore = await cookies();
    const token = cookieStore.get(COOKIE_NAME);
    if (!token) return NextResponse.json({ message: "Authentication required" }, { status: 401 });

    const { userId } = jwt.verify(token.value, JWT_SECRET);
    if (!userId) return NextResponse.json({ message: "Invalid token payload" }, { status: 401 });

    // 2. Get the new preferences data from the request body
    // This body can now contain either `topOutcomes` or `view`
    const body = await request.json();

    // 3. Find the user's existing preferences record
    const existingPrefs = await prisma.auth_user_preferences.findUnique({
      where: { user_id: userId },
    });

    if (existingPrefs) {
      // If preferences exist, merge the new settings with the old
      const currentData = existingPrefs.preferences || {};
      const updatedData = { ...currentData, ...body }; // Merges top-level keys from the body

      await prisma.auth_user_preferences.update({
        where: { user_id: userId },
        data: { preferences: updatedData },
      });
    } else {
      // If no preferences exist, create a new record
      await prisma.auth_user_preferences.create({
        data: {
          user_id: userId,
          preferences: body,
        },
      });
    }

    return NextResponse.json({ message: "Preferences updated successfully" }, { status: 200 });
  } catch (error) {
    console.error("Error updating preferences:", error);
    return NextResponse.json({ message: "An internal server error occurred" }, { status: 500 });
  }
}
