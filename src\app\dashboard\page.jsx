/*
 * =================================================================
 * FILE: src/app/dashboard/page.js (CORRECTED & DYNAMIC)
 * =================================================================
 * This is the complete and final code for the fully dynamic dashboard.
 */

"use client";

import { useState, useEffect, Fragment, useCallback } from "react";
import { ChevronLeft, ChevronRight, Settings, X, PlusCircle } from "lucide-react";
import { format, addDays, subDays, startOfWeek, endOfWeek, getISOWeek } from "date-fns";
import { DndContext, useDraggable, useDroppable, closestCenter, DragOverlay } from "@dnd-kit/core";
import { SortableContext, useSortable, arrayMove, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Dialog, Transition } from "@headlessui/react";

// --- Reusable Components ---
const PaperSection = ({ title, children, onCustomizeClick, onNewClick }) => (
  <div className="mb-12">
    <div className="flex justify-between items-center mb-4">
      <h2 className="text-2xl font-serif font-bold text-gray-700">{title}</h2>
      <div className="flex items-center space-x-2">
        {onNewClick && (
          <button onClick={onNewClick} className="p-2 rounded-full hover:bg-gray-200 transition-colors">
            <PlusCircle className="w-5 h-5 text-gray-600" />
          </button>
        )}
        {onCustomizeClick && (
          <button onClick={onCustomizeClick} className="p-2 rounded-full hover:bg-gray-200 transition-colors">
            <Settings className="w-5 h-5 text-gray-600" />
          </button>
        )}
      </div>
    </div>
    {children}
  </div>
);

const OutcomeCardUI = ({ outcome, isDragging }) => (
  <div className={`bg-white rounded-md p-3 text-sm border border-gray-200 text-gray-900 ${isDragging ? "opacity-50 shadow-2xl" : "shadow-sm"}`}>
    <p>{outcome.name}</p>
  </div>
);

const DraggableOutcomeCard = ({ outcome }) => {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({ id: `matrix-${outcome.id}`, data: { outcome } });
  const style = { transform: CSS.Translate.toString(transform) };
  return (
    <div ref={setNodeRef} style={style} {...listeners} {...attributes} className="cursor-grab">
      <OutcomeCardUI outcome={outcome} />
    </div>
  );
};

const SortableOutcomeCard = ({ outcome }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: outcome.id });
  const style = { transform: CSS.Transform.toString(transform), transition };
  return (
    <div ref={setNodeRef} style={style} {...listeners} {...attributes} className="cursor-grab">
      <OutcomeCardUI outcome={outcome} isDragging={isDragging} />
    </div>
  );
};

const DroppableTableCell = ({ rowId, subRowId, colId, children }) => {
  const { isOver, setNodeRef } = useDroppable({ id: `${rowId}-${subRowId}-${colId}`, data: { type: "cell", rowId, subRowId, colId } });
  return (
    <td ref={setNodeRef} className={`p-2 align-top border-l border-gray-200 min-h-[60px] transition-colors ${isOver ? "bg-blue-100" : ""}`}>
      <div className="space-y-2">{children}</div>
    </td>
  );
};

// --- Main Components ---
const WeekNavigator = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [weekStartsOn, setWeekStartsOn] = useState(1);
  const start = startOfWeek(currentDate, { weekStartsOn });
  const end = endOfWeek(currentDate, { weekStartsOn });
  return (
    <div className="flex flex-col items-center mb-10">
      <div className="flex items-center space-x-4">
        <button onClick={() => setCurrentDate(subDays(currentDate, 7))} className="p-2 rounded-full hover:bg-gray-200">
          <ChevronLeft className="w-5 h-5 text-gray-800" />
        </button>
        <h1 className="text-xl font-semibold text-gray-800">
          W{getISOWeek(currentDate)}: {format(start, "dd MMM")} - {format(end, "dd MMM")}
        </h1>
        <button onClick={() => setCurrentDate(addDays(currentDate, 7))} className="p-2 rounded-full hover:bg-gray-200">
          <ChevronRight className="w-5 h-5 text-gray-800" />
        </button>
      </div>
      <div className="text-xs text-gray-500 mt-2">
        Week starts on:
        <button onClick={() => setWeekStartsOn(1)} className={`ml-2 font-medium ${weekStartsOn === 1 ? "text-blue-600" : ""}`}>
          Mon
        </button>{" "}
        |
        <button onClick={() => setWeekStartsOn(0)} className={`ml-1 font-medium ${weekStartsOn === 0 ? "text-blue-600" : ""}`}>
          Sun
        </button>
      </div>
    </div>
  );
};

const TopOutcomesList = ({ topOutcomeIds = [], allOutcomes = [] }) => {
  const { isOver, setNodeRef } = useDroppable({ id: "top-outcomes-droppable", data: { type: "top-outcomes" } });
  const topOutcomes = topOutcomeIds.map((id) => allOutcomes.find((o) => o.id === id)).filter(Boolean);

  return (
    <SortableContext items={topOutcomes} strategy={verticalListSortingStrategy}>
      <div ref={setNodeRef} className={`bg-gray-50/50 p-4 rounded-lg min-h-[200px] transition-colors ${isOver ? "bg-blue-100" : ""}`}>
        <div className="space-y-2">
          {topOutcomes.map((outcome) => (
            <SortableOutcomeCard key={outcome.id} outcome={outcome} />
          ))}
        </div>
      </div>
    </SortableContext>
  );
};

const CustomizeViewModal = ({ isOpen, onClose, currentView, onSave, customFields = [] }) => {
  const [viewSettings, setViewSettings] = useState(currentView);

  const availableDimensions = [
    { id: "lifeAspect", name: "Life Aspect" },
    { id: "project", name: "Project" },
    ...customFields.map((cf) => ({ id: cf.id, name: cf.name })),
  ];

  const handleSave = () => {
    onSave(viewSettings);
    onClose();
  };
  const handleSettingChange = (key, value) => {
    setViewSettings((prev) => ({ ...prev, [key]: value }));
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 flex justify-between items-center">
                  Customize View
                  <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-200">
                    <X className="w-4 h-4" />
                  </button>
                </Dialog.Title>
                <div className="mt-4 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Group Rows By</label>
                    <select
                      value={viewSettings.rows}
                      onChange={(e) => handleSettingChange("rows", e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-gray-900"
                    >
                      {availableDimensions.map((dim) => (
                        <option key={dim.id} value={dim.id}>
                          {dim.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Then Group By</label>
                    <select
                      value={viewSettings.subRows}
                      onChange={(e) => handleSettingChange("subRows", e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-gray-900"
                    >
                      {availableDimensions.map((dim) => (
                        <option key={dim.id} value={dim.id}>
                          {dim.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Set Columns By</label>
                    <select
                      value={viewSettings.columns}
                      onChange={(e) => handleSettingChange("columns", e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-gray-900"
                    >
                      {availableDimensions.map((dim) => (
                        <option key={dim.id} value={dim.id}>
                          {dim.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="mt-6 flex justify-end">
                  <button
                    type="button"
                    className="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none"
                    onClick={handleSave}
                  >
                    Apply View
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

const CreateOutcomeModal = ({ isOpen, onClose, projects, priorityField, onSave }) => {
  const [name, setName] = useState("");
  const [projectId, setProjectId] = useState("");
  const [priorityId, setPriorityId] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (projects.length > 0) setProjectId(projects[0].id);
      if (priorityField?.choice_options?.length > 0) setPriorityId(priorityField.choice_options[0].id);
    }
  }, [isOpen, projects, priorityField]);

  const handleSave = async () => {
    if (!name.trim()) return;
    setIsLoading(true);
    try {
      await onSave({ name, projectId, priorityId, priorityFieldId: priorityField.id });
      setName("");
      onClose();
    } catch (error) {
      console.error("Failed to save outcome:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>
        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900 flex justify-between items-center">
                  New Outcome
                  <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-200">
                    <X className="w-4 h-4" />
                  </button>
                </Dialog.Title>
                <div className="mt-4 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Outcome Name</label>
                    <input
                      type="text"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-gray-900"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Project</label>
                    <select
                      value={projectId}
                      onChange={(e) => setProjectId(e.target.value)}
                      className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-gray-900"
                    >
                      {projects.map((p) => (
                        <option key={p.id} value={p.id}>
                          {p.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  {priorityField && priorityField.choice_options && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">{priorityField.name}</label>
                      <select
                        value={priorityId}
                        onChange={(e) => setPriorityId(e.target.value)}
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm text-gray-900"
                      >
                        {(priorityField.choice_options || []).map((c) => (
                          <option key={c.id} value={c.id}>
                            {c.value}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
                <div className="mt-6 flex justify-end">
                  <button
                    type="button"
                    disabled={isLoading}
                    className="inline-flex justify-center rounded-md border border-transparent bg-blue-100 px-4 py-2 text-sm font-medium text-blue-900 hover:bg-blue-200 focus:outline-none disabled:bg-blue-50"
                    onClick={handleSave}
                  >
                    {isLoading ? "Creating..." : "Create Outcome"}
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

const DynamicMatrix = ({ data, setData, onCustomizeClick, onNewClick }) => {
  const {
    view = {},
    rows = [],
    subRows = [],
    columns = [],
    grid = {},
    allOutcomes = [],
    topOutcomes = [],
    customFields = [],
    priorityFieldId,
  } = data;
  const [activeDragItem, setActiveDragItem] = useState(null);

  function handleDragStart(event) {
    const draggedId = event.active.id.toString().replace("matrix-", "");
    const outcome = allOutcomes.find((o) => o.id === draggedId);
    setActiveDragItem(outcome);
  }

  function handleDragEnd(event) {
    setActiveDragItem(null);
    const { active, over } = event;
    if (!over) return;

    const draggedId = active.id.toString().replace("matrix-", "");
    const draggedOutcome = allOutcomes.find((o) => o.id === draggedId);

    if (active.data.current?.sortable && over.data.current?.sortable) {
      const oldIndex = topOutcomes.indexOf(active.id);
      const newIndex = topOutcomes.indexOf(over.id);
      if (oldIndex !== newIndex) {
        const newTopOutcomes = arrayMove(topOutcomes, oldIndex, newIndex);
        setData((prev) => ({ ...prev, topOutcomes: newTopOutcomes }));
        fetch("/api/user/preferences", {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ topOutcomes: newTopOutcomes }),
        });
      }
      return;
    }

    if (over.id === "top-outcomes-droppable") {
      if (!topOutcomes.includes(draggedId)) {
        const newTopOutcomes = [...topOutcomes, draggedId];
        setData((prev) => ({ ...prev, topOutcomes: newTopOutcomes }));
        fetch("/api/user/preferences", {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ topOutcomes: newTopOutcomes }),
        });
      }
      return;
    }

    if (over.data.current?.type === "cell") {
      const newRowId = over.data.current.rowId;
      const newSubRowId = over.data.current.subRowId;
      const newColId = over.data.current.colId;

      const updatedGrid = JSON.parse(JSON.stringify(grid));
      Object.values(updatedGrid).forEach((row) =>
        Object.values(row).forEach((subRow) =>
          Object.values(subRow).forEach((cell) => {
            const index = cell.findIndex((o) => o.id === draggedId);
            if (index > -1) cell.splice(index, 1);
          })
        )
      );
      updatedGrid[newRowId][newSubRowId][newColId].push(draggedOutcome);
      setData((prev) => ({ ...prev, grid: updatedGrid }));

      fetch(`/api/outcomes/${draggedId}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ newProjectId: newSubRowId, newPriorityId: newColId, priorityFieldId }),
      });
    }
  }

  const rowsToRender = rows.filter((row) => grid[row.id] && Object.values(grid[row.id]).some((subRow) => Object.values(subRow).flat().length > 0));

  return (
    <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd} collisionDetection={closestCenter}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
        <div className="lg:col-span-2">
          <PaperSection title="This Week's Outcomes" onCustomizeClick={onCustomizeClick} onNewClick={onNewClick}>
            <div className="overflow-x-auto bg-gray-50/50 p-1 rounded-lg">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="p-2 text-left font-semibold text-gray-500 w-[15%] capitalize">{view.rows?.replace(/([A-Z])/g, " $1") || ""}</th>
                    <th className="p-2 text-left font-semibold text-gray-500 w-[20%] capitalize">{view.subRows?.replace(/([A-Z])/g, " $1") || ""}</th>
                    {columns.map((col) => (
                      <th key={col.id} className="p-2 text-left font-semibold text-gray-600 border-l border-gray-200">
                        {col.name}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {rowsToRender.map((row, rowIndex) => {
                    const subRowMap = grid[row.id] || {};
                    const subRowsToRender = subRows.filter((sr) => subRowMap[sr.id] && Object.values(subRowMap[sr.id]).flat().length > 0);

                    return subRowsToRender.map((subRow, subRowIndex) => {
                      const cellMap = subRowMap[subRow.id] || {};
                      return (
                        <tr
                          key={`${row.id}-${subRow.id}`}
                          className={`${rowIndex > 0 && subRowIndex === 0 ? "border-t-2 border-gray-300" : "border-t border-gray-200"}`}
                        >
                          {subRowIndex === 0 && (
                            <td rowSpan={subRowsToRender.length} className="p-2 align-top font-bold text-gray-700">
                              {row.name}
                            </td>
                          )}
                          <td className="p-2 align-top font-semibold text-gray-600">{subRow.name}</td>
                          {columns.map((col) => (
                            <DroppableTableCell key={col.id} rowId={row.id} subRowId={subRow.id} colId={col.id}>
                              {(cellMap[col.id] || []).map((outcome) => (
                                <DraggableOutcomeCard key={outcome.id} outcome={outcome} />
                              ))}
                            </DroppableTableCell>
                          ))}
                        </tr>
                      );
                    });
                  })}
                </tbody>
              </table>
            </div>
          </PaperSection>
        </div>
        <div>
          <PaperSection title="Top Outcomes">
            <TopOutcomesList topOutcomeIds={topOutcomes} allOutcomes={allOutcomes} />
          </PaperSection>
        </div>
      </div>
      <DragOverlay>{activeDragItem ? <OutcomeCardUI outcome={activeDragItem} isDragging /> : null}</DragOverlay>
    </DndContext>
  );
};

export default function DashboardPage() {
  const [matrixData, setMatrixData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isCustomizeModalOpen, setIsCustomizeModalOpen] = useState(false);
  const [isNewOutcomeModalOpen, setIsNewOutcomeModalOpen] = useState(false);

  const [view, setView] = useState({
    rows: "lifeAspect",
    subRows: "project",
    columns: "priority",
  });

  const fetchData = useCallback(async (currentView) => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams(currentView);
      const response = await fetch(`/api/project-matrix?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch matrix data");
      const data = await response.json();
      setMatrixData(data);
      // Check for saved view settings in preferences and update if they exist
      if (data.preferences?.view) {
        setView(data.preferences.view);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    const fetchInitialData = async () => {
      // This logic will be improved to fetch user's saved view first
      await fetchData(view);
    };
    fetchInitialData();
  }, [fetchData]);

  const handleSaveView = async (newView) => {
    await fetch("/api/user/preferences", {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ view: newView }),
    });
    fetchData(newView);
  };

  const handleCreateOutcome = async (outcomeData) => {
    try {
      const response = await fetch("/api/outcomes", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(outcomeData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create outcome");
      }
      await fetchData(view);
    } catch (error) {
      console.error("Creation failed:", error);
    }
  };

  if (isLoading) return <div className="flex justify-center items-center min-h-screen">Loading dashboard...</div>;
  if (error) return <div className="flex justify-center items-center min-h-screen text-red-500">Error: {error}</div>;

  return (
    <div className="bg-[#F7F7F7] min-h-screen p-4 sm:p-8 md:p-12 font-sans">
      <div className="max-w-screen-xl mx-auto bg-white p-8 sm:p-12 rounded-lg shadow-md">
        <WeekNavigator />
        {matrixData && (
          <DynamicMatrix
            data={matrixData}
            setData={setMatrixData}
            onCustomizeClick={() => setIsCustomizeModalOpen(true)}
            onNewClick={() => setIsNewOutcomeModalOpen(true)}
          />
        )}
      </div>

      {matrixData && (
        <CustomizeViewModal
          isOpen={isCustomizeModalOpen}
          onClose={() => setIsCustomizeModalOpen(false)}
          currentView={view}
          onSave={handleSaveView}
          customFields={matrixData.customFields || []}
        />
      )}
      {matrixData && (
        <CreateOutcomeModal
          isOpen={isNewOutcomeModalOpen}
          onClose={() => setIsNewOutcomeModalOpen(false)}
          projects={matrixData.projects.filter((p) => p.id !== "undefined")}
          priorityField={matrixData.customFields?.find((cf) => cf.name.toLowerCase() === "priority")}
          onSave={handleCreateOutcome}
        />
      )}
    </div>
  );
}
