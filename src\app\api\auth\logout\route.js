import { serialize } from "cookie";
import { NextResponse } from "next/server";

const COOKIE_NAME = "authToken";

export async function POST() {
  // To log a user out, we send back a cookie with the same name,
  // but with an empty value and a 'maxAge' of 0 or a past date.
  // This tells the browser to immediately delete the cookie.
  const serializedCookie = serialize(COOKIE_NAME, "", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
    maxAge: -1, // Set to a past time
    path: "/",
  });

  const response = NextResponse.json({ message: "Logged out successfully" }, { status: 200 });
  response.headers.set("Set-Cookie", serializedCookie);

  return response;
}
