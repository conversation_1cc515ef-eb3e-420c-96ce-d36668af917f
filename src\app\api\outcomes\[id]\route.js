import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function PATCH(request, { params }) {
  const { id: outcomeId } = await params; // Fix for the sync-dynamic-apis warning
  try {
    const body = await request.json();
    const { newProjectId, newPriorityId, priorityFieldId } = body;

    if (!outcomeId || !priorityFieldId) {
      return NextResponse.json({ message: "Missing required fields for update" }, { status: 400 });
    }

    // Use a transaction to perform the update safely.
    await prisma.$transaction(async (tx) => {
      // 1. Update the outcome's parent project
      const finalProjectId = newProjectId === "undefined" ? null : newProjectId;
      await tx.workitems_outcome.update({
        where: { id: outcomeId },
        data: { project_id: finalProjectId },
      });

      // 2. Find any existing priority value for this outcome
      const existingValue = await tx.workitems_custom_field_value.findFirst({
        where: { outcome_id: outcomeId, field_definition_id: priorityFieldId },
      });

      // 3. Handle the new priority
      if (newPriorityId === "undefined") {
        if (existingValue) {
          await tx.workitems_custom_field_value.delete({ where: { id: existingValue.id } });
        }
      } else {
        if (existingValue) {
          await tx.workitems_custom_field_value.update({
            where: { id: existingValue.id },
            data: { selected_options: { set: [{ id: newPriorityId }] } },
          });
        } else {
          await tx.workitems_custom_field_value.create({
            data: {
              outcome_id: outcomeId,
              field_definition_id: priorityFieldId,
              selected_options: { connect: { id: newPriorityId } },
            },
          });
        }
      }
    });

    return NextResponse.json({ message: "Outcome updated successfully" }, { status: 200 });
  } catch (error) {
    console.error(`Error updating outcome ${outcomeId}:`, error);
    return NextResponse.json({ message: "An internal server error occurred" }, { status: 500 });
  }
}
